name: Build The App

on:
    push:
        branches:
            - main

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
        - name: Checkout Repository
          uses: actions/checkout@v3

        - name: Node Setup
          uses: actions/setup-node@v3
          with:
            node-version: '12.x'

        - name: Install Dependencies
          run: |
            npm install
        - name: Build the App
          run: |
            npx nuxi generate
    deploy:
        runs-on: ubuntu-latest

        needs: build

        steps:
        - name: Deploy via executing remote commands
          uses: appleboy/ssh-action@v1.0.0
          with:
            HOST: ${{ secrets.SSH_HOST }}
            USERNAME: ${{ secrets.USERNAME }}
            PORT: ${{ secrets.SSH_PORT }}
            KEY: ${{ secrets.SSH_PRIVATE_KEY }}
            script: |
                cd ${{ secrets.FRONTEND_DIR_PATH }}
                git pull origin main
                export NVM_DIR=~/.nvm
                source ~/.nvm/nvm.sh
                nvm use 12
                npm install
                npx nuxi generate
