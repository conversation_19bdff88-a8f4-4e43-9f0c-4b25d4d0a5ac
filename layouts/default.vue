<script setup>
    const config = useRuntimeConfig();

    const darkMode = useDarkMode();

    const isSubscribed = useSubscribed().isSubscribed;

    const hasActiveBilling = useSubscribed().hasActiveBilling;

    const podcast = usePlayer().podcast;

    const playerOpen = usePlayer().isOpen;

    const checkSubscription = async () => {
        if (localStorage.getItem('user_access_token')) {
            const res = await $fetch(config.public.BASE_URL_API + '/auth/me', {
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            });

            if (res.is_subscribed) {
                useSubscribed().isSubscribed.value = true;

                if (res.has_active_billing) {
                    useSubscribed().hasActiveBilling.value = true;
                }
            }
        }
    }

    onBeforeMount(() => {
        darkMode.value = JSON.parse(localStorage.getItem('darkMode')) || false;

        checkSubscription();
    });
</script>

<template>
    <div class="font-zain" :class="darkMode && 'dark'" dir="rtl">
        <div class="flex flex-col min-h-screen bg-white dark:bg-gray-900">
            <navbar />
    
            <main class="flex-1 pt-12 lg:mt-24">
                <slot />
            </main>
    
            <footer>
                <div class="flex flex-col items-center justify-between px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8 lg:flex-row">
                    <div class="flex space-x-4">
                        <NuxtLink to="/" class="inline-flex items-center text-xl font-bold text-sky-500 mx-4">
                            قوافي
                        </NuxtLink>
                        
                        <NuxtLink exactActiveClass="dark:text-white border-sky-400 text-gray-900 border-b-2" to="/about" class="inline-flex items-center px-1 pt-1 text-sm font-semibold text-gray-500 dark:text-gray-300 hover:text-gray-700">عن المنصة</NuxtLink>
                        <NuxtLink exactActiveClass="dark:text-white border-sky-400 text-gray-900 border-b-2" to="/terms" class="inline-flex items-center px-1 pt-1 text-sm font-semibold text-gray-500 dark:text-gray-300 hover:text-gray-700">الشروط والأحكام</NuxtLink>
                    </div>

    
                    <p class="mt-6 text-sm font-semibold text-gray-500 lg:mt-0 dark:text-gray-300">© جميع الحقوق محفوظة {{  (new Date().getFullYear()) }}. </p>

                    <p class="text-gray-600 mt-2">
                        Provided by <span class="text-black">Digital Plus Technology Co. Ltd</span>
                    </p>
                </div>
            </footer>
        </div>

        <div v-if="playerOpen && (isSubscribed && hasActiveBilling)">
            <player :podcast="podcast" />
        </div>
    </div>
</template>
