<script setup>
    defineProps({
        data:  Object,
    });

    const config = useRuntimeConfig();

</script>

<template>
    <NuxtLink :to="'/poetries/' + data.id" class="relative z-20 flex flex-col w-56 h-64 p-6 overflow-hidden rounded-lg hover:opacity-75 xl:w-auto">
        <span aria-hidden="true" class="absolute inset-0">
            <img :src="config.public.BASE_URL + data.thumbnail" alt="" class="object-cover object-center w-full h-full">
        </span>
        
        <span aria-hidden="true" class="absolute inset-x-0 bottom-0 via-gray-800 opacity-70 h-2/3 bg-gradient-to-t from-gray-800"></span>
        
        <div class="relative flex flex-col items-center justify-center mt-auto">
            <p v-if="!data.is_premium" class="px-3 py-2 text-sm font-bold rounded-full bg-gray-900/50 text-sky-400">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                </svg>
            </p>
            <h2 class="text-xl font-semibold text-center text-white truncate" v-text="data.name_ar"></h2>
            <p class="text-xl font-semibold text-center text-sky-400" v-text="data.poet.name_ar"></p>
        </div>
    </NuxtLink>
</template>
