<script setup>
    const config = useRuntimeConfig();

    const isSubscribed = useSubscribed().isSubscribed;

    const isOpen = ref(false);

    const dropdownOpen = ref(false);

    const darkMode = useDarkMode();

    const phone = ref('N/A');

    const changeDarkMode = () => {
        darkMode.value = !darkMode.value

        localStorage.setItem('darkMode', JSON.stringify(darkMode.value));
    }

    const getUserData = async () => {
        if (localStorage.getItem('user_phone')) {
            phone.value = localStorage.getItem('user_phone');
        }
    }

    const logout = async () => {
        if (localStorage.getItem('user_access_token')) {
              await $fetch(config.public.BASE_URL_API + '/auth/logout', {
                  method: 'POST',
                  headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
              }).then(() => {
                  localStorage.removeItem('user_access_token');
                  localStorage.removeItem('user_phone');

                  window.location.href = '/';
              });
        }
    }

    onMounted(() => {
      getUserData();
    });
</script>

<template>
    <div class="z-20 w-full bg-white/80 lg:fixed dark:bg-slate-900/50">
        <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div class="flex justify-between h-16 lg:h-20">
                <div class="flex gap-x-4">
                    <NuxtLink to="/" class="flex items-center text-xl font-bold md:text-2xl text-sky-500">
                      قوافي
                    </NuxtLink>

                    <div class="hidden lg:mx-6 lg:flex lg:gap-x-8">
                      <NuxtLink exactActiveClass="dark:text-white border-sky-400 text-gray-900 border-b-2" to="/" class="inline-flex items-center px-1 pt-1 text-sm font-semibold text-gray-500 dark:text-gray-300 hover:text-gray-700 ">الرئيسية</NuxtLink>
                      <NuxtLink exactActiveClass="dark:text-white border-sky-400 text-gray-900 border-b-2" to="/poetries" class="inline-flex items-center px-1 pt-1 text-sm font-semibold text-gray-500 dark:text-gray-300 hover:text-gray-700">الشِعر</NuxtLink>
                      <NuxtLink exactActiveClass="dark:text-white border-sky-400 text-gray-900 border-b-2" to="/poets" class="inline-flex items-center px-1 pt-1 text-sm font-semibold text-gray-500 dark:text-gray-300 hover:text-gray-700">الشعراء</NuxtLink>
                    </div>
                </div>
                
                <div class="flex items-center gap-x-1 md:gap-x-4">
                  <search-form class="hidden sm:block" />

                  <div v-cloak class="hidden md:block">
                    <cancel-subscription v-if="isSubscribed" />

                    <div v-else class="flex items-center gap-x-4">
                      <NuxtLink to="/login" class="relative duration-200 transition-colors inline-flex items-center gap-x-1.5 rounded-md bg-sky-100 px-3 py-2 text-sm font-semibold text-sky-500 shadow-sm hover:bg-sky-200 dark:bg-sky-500/20 dark:hover:bg-sky-500/60 dark:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600">
                        تسجيل الدخول
                      </NuxtLink>

                      <a href="https://dsplp.sd.zain.com/?p=7596994161" class="relative duration-200 transition-colors inline-flex items-center gap-x-1.5 rounded-md bg-sky-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600">
                        اشترك الان
                      </a>
                    </div>
                  </div>

                  <div v-if="isSubscribed" class="relative ">
                    <button @click="dropdownOpen = !dropdownOpen" class="flex items-center justify-center p-2 text-gray-500 rounded-md focus:outline-none dark:text-gray-300 hover:bg-gray-100 hover:text-gray-500 dark:hover:bg-gray-800 focus:ring-2 focus:ring-inset focus:ring-sky-500">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                      </svg>
                    </button>

                      <div @click="dropdownOpen = false" v-show="dropdownOpen" class="fixed inset-0"></div>

                    <transition 
                      enter-active-class="duration-150 ease-out" 
                      enter-from-class="scale-90 opacity-0" 
                      enter-to-class="scale-100 spacity-100" 
                      leave-active-class="duration-100 ease-in" 
                      leave-from-class="scale-100 opacity-100" 
                      leave-to-class="scale-90 opacity-0"
                    >
                      <div @click="dropdownOpen = false" v-show="dropdownOpen">
                          <div class="absolute left-0 z-20 mt-4 overflow-hidden origin-top-right bg-white rounded-md shadow-xl sm:w-56 w-52 dark:bg-gray-800" >
                            <NuxtLink exactActiveClass="dark:text-white bg-sky-400 text-gray-900" to="/profile" class="flex items-center px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 gap-x-2 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ">
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                              </svg>

                              <div>
                                الملف الشخصي
                                <p v-text="phone" class="-mt1 text-sm"></p>
                              </div>
                            </NuxtLink>

                            <NuxtLink exactActiveClass="dark:text-white bg-sky-400 text-gray-900" to="/favorites" class="flex items-center px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 gap-x-2 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ">
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                              </svg>

                              المفضلة
                            </NuxtLink>

                            <button @click="changeDarkMode()" class="w-full px-4 py-3 text-gray-500 transition-colors duration-300 transform rounded-lg dark:text-gray-300 dark:hover:bg-gray-800 hover:bg-gray-100 focus:outline-none" aria-label="darkMode mode button">
                              <p v-show="!darkMode" class="flex items-center gap-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 sm:w-6 sm:h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
                                </svg>
                                الوضع الداكن
                              </p>

                              <p v-show="darkMode" class="flex items-center gap-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 sm:w-6 sm:h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                                </svg>
                                الوضع المضيئ
                              </p>
                          </button>

                            <button @click="logout" class="flex items-center w-full px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 focus:outline-none gap-x-2 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ">
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 rotate-180">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                              </svg>

                              تسجيل خروج
                            </button>
                          </div>
                      </div>
                    </transition>
                  </div>

                  <img class="w-16 m-2 md:w-24 " src="../assets/svgs/zain-logo.svg" alt="">

                  <button @click="isOpen =  !isOpen" class="items-center justify-center p-2 text-gray-500 rounded-md focus:outline-none dark:text-gray-300 lg:hidden hover:bg-gray-100 hover:text-gray-500 dark:hover:bg-gray-800 focus:ring-2 focus:ring-inset focus:ring-sky-500">
                    <span class="sr-only">Open main menu</span>
                    <svg v-if="!isOpen" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                      <path fill-rule="evenodd" d="M3 6.75A.75.75 0 013.75 6h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 6.75zM3 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H3.75A.75.75 0 013 12zm0 5.25a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75a.75.75 0 01-.75-.75z" clip-rule="evenodd" />
                    </svg>

                    <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                      <path fill-rule="evenodd" d="M5.47 5.47a.75.75 0 011.06 0L12 10.94l5.47-5.47a.75.75 0 111.06 1.06L13.06 12l5.47 5.47a.75.75 0 11-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 01-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 010-1.06z" clip-rule="evenodd" />
                    </svg>
                  </button>


                </div>

              </div>
        </div>

        <div v-cloak class="flex-shrink-0 px-4 mx-auto mt-2 max-w-7xl sm:px-6 lg:px-8 sm:hidden">
          <cancel-subscription v-if="isSubscribed" />

          <div v-else class="flex items-center gap-x-4 px-3">
            <NuxtLink to="/login" class="relative w-full text-center duration-200 transition-colors inline-flex justify-center gap-x-1.5 rounded-md bg-sky-100 px-3 py-2 text-sm font-semibold text-sky-500 shadow-sm hover:bg-sky-200 dark:bg-sky-500/20 dark:hover:bg-sky-500/60 dark:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600">
              تسجيل الدخول
            </NuxtLink>

            <a href="https://dsplp.sd.zain.com/?p=7596994161" class="relative w-full duration-200 text-center justify-center transition-colors inline-flex items-center gap-x-1.5 rounded-md bg-sky-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600">
              اشترك الان
            </a>
          </div>

          <search-form class="block md:hidden mt-4" />
        </div>
        
        <div @click="isOpen = false" v-show="isOpen" class="fixed inset-0"></div>

        <transition enter-active-class="duration-150 ease-out" enter-from-class="scale-95 opacity-0" enter-to-class="scale-100 spacity-100" leave-active-class="duration-100 ease-in" leave-from-class="scale-100 opacity-100" leave-to-class="scale-95 opacity-0">
          <div @click="isOpen = false" v-show="isOpen">
              <div class="absolute inset-x-0 z-50 pb-4 mx-4 mt-4 space-y-1 overflow-hidden bg-white rounded-lg shadow-md dark:bg-gray-800" >
                <NuxtLink exactActiveClass="dark:bg-gray-700 bg-gray-100 text-sky-500 dark:text-sky-300" to="/" class="block px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 dark:text-gray-300 ">الرئيسية</NuxtLink>
                <NuxtLink exactActiveClass="dark:bg-gray-700 bg-gray-100 text-sky-500 dark:text-sky-300" to="/poetries" class="block px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 dark:text-gray-300 ">الشِعر</NuxtLink>
                <NuxtLink exactActiveClass="dark:bg-gray-700 bg-gray-100 text-sky-500 dark:text-sky-300" to="/poets" class="block px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 dark:text-gray-300 ">الشعراء</NuxtLink>
                <NuxtLink exactActiveClass="dark:bg-gray-700 bg-gray-100 text-sky-500 dark:text-sky-300" to="/about" class="block px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 dark:text-gray-300 ">عن المنصة</NuxtLink>
                <NuxtLink exactActiveClass="dark:bg-gray-700 bg-gray-100 text-sky-500 dark:text-sky-300" to="/terms" class="block px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 dark:text-gray-300 ">الشروط والأحكام</NuxtLink>
                <button v-if="isSubscribed" @click="logout" class="flex items-center w-full px-4 py-3 text-base font-medium text-gray-500 transition-colors duration-200 focus:outline-none gap-x-2 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 rotate-180">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                  </svg>

                  تسجيل خروج
                </button>
              </div>
          </div>
        </transition>
    </div>
</template>
