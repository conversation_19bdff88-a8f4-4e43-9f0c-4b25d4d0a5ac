<script setup>
    defineProps({
        num:  Number,
    });
</script>

<template>
    <div class="absolute flex gap-6 px-4 animate-pulse min-w-screen-xl sm:px-6 lg:px-8 xl:relative xl:grid xl:grid-cols-5 xl:px-0">
        <div v-for="i in num" :key="i" class="relative flex flex-col w-56 h-64 p-6 overflow-hidden bg-gray-300 rounded-lg dark:bg-gray-800 hover:opacity-75 xl:w-auto"></div>
    </div>
</template>
