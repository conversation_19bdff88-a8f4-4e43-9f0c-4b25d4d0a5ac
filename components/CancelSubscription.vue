<script setup>
    const config = useRuntimeConfig();

    const showModal = ref(false);

    const unsubscribe = async () => {
        if (localStorage.getItem('user_access_token')) {
            const res = await $fetch(config.public.BASE_URL_API + '/DSP/unsubscribe', {
                method: 'POST',
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            }).then(() => {
              localStorage.removeItem('user_access_token');
              localStorage.removeItem('user_phone');

              window.location.href = '/';
            });
        }
    }
</script>

<template>
    <button @click="showModal = true" href="#" class="relative focus:outline-none duration-200 transition-colors inline-flex items-center gap-x-1.5 rounded-md dark:bg-sky-900 px-3 py-3 w-full sm:w-auto justify-center sm:py-2 text-sm font-semibold bg-sky-100 text-sky-700 hover:text-white dark:text-white shadow-sm dark:hover:bg-red-500 hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-500">
        الغاء الاشترك
    </button>

    <div v-show="showModal" class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <!--
    Background backdrop, show/hide based on modal state.

    Entering: "ease-out duration-300"
      From: "opacity-0"
      To: "opacity-100"
    Leaving: "ease-in duration-200"
      From: "opacity-100"
      To: "opacity-0"
  -->
  <div @click="showModal = false" class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"></div>

  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-full p-4 text-center sm:items-center sm:p-0">
      <!--
        Modal panel, show/hide based on modal state.

        Entering: "ease-out duration-300"
          From: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          To: "opacity-100 translate-y-0 sm:scale-100"
        Leaving: "ease-in duration-200"
          From: "opacity-100 translate-y-0 sm:scale-100"
          To: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      -->
      <div class="relative w-full px-4 pt-5 pb-4 overflow-hidden text-left transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:max-w-lg sm:p-6">
        <div class="sm:flex sm:items-start">
          <div class="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-red-100 rounded-full sm:mx-0 sm:h-10 sm:w-10">
            <svg class="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mx-4 sm:mt-0 sm:text-right">
            <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">الغاء الاشترك</h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500">هل تود الغاء اشتراكك في خدمة قوافي</p>
            </div>
          </div>
        </div>
        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
          <button @click="unsubscribe" type="button" class="inline-flex justify-center w-full px-3 py-2 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-500 sm:mr-3 sm:w-auto">
            الغاء الاشترك
          </button>
          <button @click="showModal = false" type="button" class="inline-flex justify-center w-full px-3 py-2 mt-3 text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-200 hover:bg-gray-50 sm:mt-0 sm:w-auto">
            تراجع
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

</template>
