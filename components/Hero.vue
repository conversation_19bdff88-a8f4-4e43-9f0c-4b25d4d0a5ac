<script setup>
    const isSubscribed = useSubscribed().isSubscribed;
</script>

<template>
  <div>
    <main>
      <div class="relative isolate">
        <div class="absolute top-0 right-0 -ml-24 overflow-hidden left-1/2 -z-10 transform-gpu blur-3xl lg:ml-24 xl:ml-48">
          <svg viewBox="0 0 801 1036" aria-hidden="true" class="w-[50.0625rem]">
            <path fill="url(#70656b7e-db44-4b9b-b7d2-1f06791bed52)" fill-opacity=".3" d="m282.279 843.371 32.285 192.609-313.61-25.32 281.325-167.289-58.145-346.888c94.5 92.652 277.002 213.246 251.009-45.597C442.651 127.331 248.072 10.369 449.268.891c160.956-7.583 301.235 116.434 351.256 179.39L507.001 307.557l270.983 241.04-495.705 294.774Z" />
            <defs>
              <linearGradient id="70656b7e-db44-4b9b-b7d2-1f06791bed52" x1="508.179" x2="-28.677" y1="-116.221" y2="1091.63" gradientUnits="userSpaceOnUse">
                <stop stop-color="#9089FC" />
                <stop offset="1" stop-color="#FF80B5" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div class="overflow-hidden">
          <div class="px-6 pb-32 mx-auto max-w-7xl lg:px-8">
            <div class="max-w-2xl mx-auto gap-x-14 lg:mx-0 lg:flex lg:max-w-none lg:items-center">
              <div class="w-full max-w-xl lg:shrink-0 xl:max-w-2xl">
                <h1 class="pt-2 text-4xl font-semibold tracking-tight text-gray-900 dark:text-white font-zain sm:text-5xl">قوافي، للشِعر منبر</h1>
                <p class="relative mt-6 text-lg leading-8 text-gray-600 dark:text-gray-400 sm:max-w-md lg:max-w-none">قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.</p>

                <div v-cloak class="hidden mt-4 sm:block">
                  <a v-if="!isSubscribed" href="https://dsplp.sd.zain.com/?p=7596994161" class="relative inline-flex items-center gap-x-1.5 rounded-md bg-sky-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600">
                    اشترك الان
                  </a>
                </div>
              </div>

              <div class="flex justify-end gap-8 mt-14 sm:-mt-16 sm:justify-start sm:pl-20 lg:mt-0 lg:pl-0">
                <div class="flex-none pt-32 ml-auto space-y-8 w-44 sm:ml-0 sm:pt-80 lg:order-last lg:pt-36 xl:order-none xl:pt-80">
                  <NuxtLink to="/poets/6" class="relative block duration-200 shadow-xl hover:scale-110 hover:shadow-sky-500/20 rounded-xl">
                    <img src="https://qawafi.mvas.digital/storage/programs/2021/03/bYKuwfbrUrzJ6gAL5dpafuLX0z3xMDXdebttKPix.jpg" alt="" class="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg" />
                  </NuxtLink>
                </div>
                
                <div class="flex-none mr-auto space-y-8 w-44 sm:mr-0 sm:pt-52 lg:pt-36">
                  <NuxtLink to="/poets/11" class="relative block duration-200 shadow-xl hover:scale-110 hover:shadow-sky-500/20 rounded-xl">
                    <img src="https://qawafi.mvas.digital/storage/programs/2021/03/If5STv56eeBSKTc5lPxD0oiGA9tKxZam7537wVmv.jpg" alt="" class="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg" />
                  </NuxtLink>
                  
                  <NuxtLink to="/poets/21" class="relative block duration-200 shadow-xl hover:scale-110 hover:shadow-sky-500/20 rounded-xl">
                    <img src="https://qawafi.mvas.digital/storage/programs/2021/12/i7qY1iQANMzG6v7aAVsl8wCB3ZB5nzoTDH5JyQKm.jpg" alt="" class="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg" />
                  </NuxtLink>
                </div>

                <div class="flex-none pt-32 space-y-8 w-44 sm:pt-0">
                  <NuxtLink to="/poets/13" class="relative block duration-200 shadow-xl mt-9 hover:scale-110 hover:shadow-sky-500/20 rounded-xl">
                    <img src="https://qawafi.mvas.digital/storage/programs/2021/12/N3C5faO907JY1sUW44aSpC16zF7YHPFJFLIDU1AI.jpg" alt="" class="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg" />
                  </NuxtLink>

                  <NuxtLink to="/poets/12" class="relative block duration-200 shadow-xl hover:scale-110 hover:shadow-sky-500/20 rounded-xl">
                    <img src="https://qawafi.mvas.digital/storage/programs/2021/03/ZT4fW8cZYrkxXdpxzMVlG63YA15eTX0WYnNvf6Py.jpg" alt="" class="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg" />
                  </NuxtLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
