<script setup>
    const { searchOnly = false, perPage = 20 } = defineProps({
        searchOnly: <PERSON><PERSON><PERSON>,
        perPage: Number,
    });

    const config = useRuntimeConfig();
    const route = useRoute();

    const poets = ref([]);

    const pending = ref(false);

    const processing = ref(false);

    const searchQuery = ref('');

    let page = 1;

    const fetch = async () => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const response  = await $fetch(config.public.BASE_URL_API + '/poets?page=' + page + '&per_page=' + perPage +'&q=' + searchQuery.value);

        poets.value.push(...response.data);

        pending.value = false;

        if (page >= response.meta.last_page) {
            page = null;
            return;
        }

        page = response.meta.current_page + 1;

        processing.value = false;
    }

    onBeforeMount(() => {
        if (route.query.q) {
            searchQuery.value = route.query.q;
        }

        if (!searchOnly || (searchOnly && searchQuery.value.length > 0)) {
            fetch();
        } else {
            page = null;
        }
    });
</script>

<template>
    <section class="pt-6 pb-24">
        <card-skeleton v-if="pending" :num="10" />

        <div v-else>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                <NuxtLink :to="'/poets/' + poet.id" v-for="poet in poets" :key="poet.id" class="relative flex flex-col p-6 overflow-hidden rounded-lg h-80 md:h-64 hover:opacity-75 xl:w-auto">
                    <span aria-hidden="true" class="absolute inset-0">
                        <img :src="config.public.BASE_URL + poet.thumbnail" alt="" class="object-cover object-center w-full h-full">
                    </span>
                    
                    <span aria-hidden="true" class="absolute inset-x-0 bottom-0 via-gray-800 opacity-70 h-2/3 bg-gradient-to-t from-gray-800"></span>
                    <div class="relative mt-auto">
                        <p class="text-xl font-semibold text-center text-white truncate" v-text="poet.name_ar"></p>
                    </div>
                </NuxtLink>
            </div>

            <div v-if="!pending && poets.length == 0" class="text-center mt-8">
                    <p class="text-gray-700 dark:text-white mb-1">لم يتم العثور على أي شاعر مطابق</p>
                    <p class="text-gray-500 dark:text-white">يرجى التحقق من حقل البحث أو تجربة كلمات مختلفة</p>
                </div>

            <div v-if="page" class="flex items-center justify-center mt-8">
                <button @click.prevent="fetch" class="px-6 py-2 text-gray-700 transition-colors duration-200 border border-gray-200 rounded-lg dark:text-white dark:border-white hover:bg-gray-100 dark:hover:bg-gray-800 ">
                    تصفح المزيد
                </button>
            </div>
        </div>
    </section>
</template>
