<script setup>
    import { ref, reactive } from "vue";
    import { How<PERSON>, Howler } from "howler";

    const props = defineProps({
        podcast: Object
    });

    const config = useRuntimeConfig();

    const audios = ref([
      {
          url: config.public.BASE_URL + props.podcast.url,
      }
    ]);

    let optionRepeat = true;
    let optionShuffle = false;
    const index = ref(0);

    let lastLoggedTime = ref(0);
    const loggingOffset = 30; // Log progress in seconds
    const step = ref(0);
    const nextButton = ref(true);
    const prevButton = ref(true);
    const random = ref(optionShuffle);
    const repeat = ref(optionRepeat);
    const duration = ref("00:00");
    const timer = ref("00:00");
    const pauseTrack = ref(false);
    const progress = ref(null);
    const volBar = ref(null);
    const sliderBtn = ref(0);
    const volumeProgress = ref(90);
    const mutePlayer = ref(false);
    const state = reactive({
      audioPlaying: [],
    });

    function formatTime(secs) {
      var minutes = Math.floor(secs / 60) || 0;
      var seconds = Math.floor(secs - minutes * 60) || 0;

      return (
        (minutes < 10 ? "0" : "") +
        minutes +
        ":" +
        (seconds < 10 ? "0" : "") +
        seconds
      );
    }
    function play() {
      var sound;
      var audio = audios.value[index.value];
      if (audio.howl) {
        sound = audio.howl;
      } else {
        state.audioPlaying[index.value] = false;
        sound = audio.howl = new Howl({
          src: [audio.url],
          html5: true, // A live stream can only be played through HTML5 Audio.
          format: ["mp3", "aac"],
          onplay: function () {
            pauseTrack.value = true;
            nextButton.value = true;
            prevButton.value = true;
            duration.value = formatTime(sound.duration());
            requestAnimationFrame(stepFunction.bind(this));
            prepareLogData(sound.seek())
          },
          onpause: function () {
            pauseTrack.value = false;
            prepareLogData(sound.seek())
          },
          onend: function () {
            prepareLogData(sound.seek())
            stop();
          },
          onseek: function () {
            window.requestAnimationFrame(stepFunction.bind(this));
          },
        });
      }

      sound.play();

      state.audioPlaying[index.value] = true;
    }
    function pause(indexo) {
      var audio = audios.value[index.value].howl;

      if (audio) {
        audio.pause();
        pauseTrack.value = false;
        state.audioPlaying[index.value] = false;
      }
    }

    function stop() {
      var audio = audios.value[index.value].howl;

      if (audio) {
        audio.stop();
        pauseTrack.value = false;
        state.audioPlaying[index.value] = false;
      }
    }

    function stepFunction() {
      var sound = audios.value[index.value].howl;
      var seek = sound.seek();
      timer.value = formatTime(Math.round(seek));
      step.value = (seek * 100) / sound.duration();

      if (progress.value) {
        sliderBtn.value =
          progress.value.offsetWidth * (step.value / 100) +
          progress.value.offsetWidth * 0.05 -
          25;
      }

      if (sound.playing()) {
        // store.commit('SET_IS_PLAYING', true)
        window.requestAnimationFrame(stepFunction.bind(this));
        onPlayerTimeUpdated()
      } else {
        // store.commit('SET_IS_PLAYING', false)
      }
    }

    function seek(event) {
      var per = event.offsetX / progress.value.clientWidth;

      var sound = audios.value[index.value].howl;

      if (sound) {
        if (sound.playing()) {
          sound.pause();
          sound.seek(sound.duration() * per);
          var barWidth = (per * 100) / 100;
          sliderBtn.value =
            progress.value.offsetWidth * barWidth +
            progress.value.offsetWidth * 0.05 -
            25;
          sound.play();
        } else {
          sound.seek(sound.duration() * per);
          var barWidth = (per * 100) / 100;
          sliderBtn.value =
            progress.value.offsetWidth * barWidth +
            progress.value.offsetWidth * 0.05 -
            25;
        }
      }
    }

    function next() {
      nextButton.value = false;
      var audio = audios.value[index.value].howl;

      prepareLogData(audio.seek())

      state.audioPlaying[index.value] = false;

      mutePlayer.value ? (mutePlayer.value = false) : "";
      audio && audio.mute(true) ? audio.mute(false) : "";

      if (audio && audios.value.length - 1 == index.value) {
        audio.stop();

        repeat.value
          ? (index.value = index.value)
          : random.value
          ? (index.value = Math.floor(Math.random() * audios.value.length))
          : (index.value = 0);
      } else {
        if (audio) {
          audio.stop();
        }

        repeat.value
          ? (index.value = index.value)
          : random.value
          ? (index.value = Math.floor(Math.random() * audios.value.length))
          : index.value++;
      }

      play();
    }

    function previous() {
      var audio = audios.value[index.value].howl;

      prepareLogData(audio.seek())

      prevButton.value = false;
      state.audioPlaying[index.value] = false;

      mutePlayer.value ? (mutePlayer.value = false) : "";
      audio && audio.mute(true) ? audio.mute(false) : "";

      if (!audio) {
        index.value = audios.value.length - 1;
      } else if (audio && index.value == 0) {
        audio.stop();

        repeat.value
          ? (index.value = index.value)
          : random.value
          ? (index.value = Math.floor(Math.random() * audios.value.length))
          : (index.value = audios.value.length - 1);
      } else if (audio) {
        audio.stop();

        repeat.value
          ? (index.value = index.value)
          : random.value
          ? (index.value = Math.floor(Math.random() * audios.value.length))
          : index.value--;
      }

      play();
    }
    function selectSound(indexSelected) {
      var audio = audios.value[index.value].howl;

      if (audio) {
        audio.stop();
        state.audioPlaying[index.value] = false;
      }

      index.value = indexSelected;

      play();
    }

    function toggleRepeat () {
      repeat.value = !repeat.value
      // store.commit('UPDATE_PLAYER_OPTION_REPEAT', repeat.value)
    }

    function toggleRandom () {
      random.value = !random.value
      // store.commit('UPDATE_PLAYER_OPTION_SHUFFLE', random.value)
    }

    function volume(event) {
      var per = event.layerX / parseFloat(volBar.value.scrollWidth);
      var barWidth = (per * 100) / 100;
      volumeProgress.value = barWidth * 100;
    //   sliderBtnVol.value =
    //     volBar.value.offsetWidth * barWidth +
    //     volBar.value.offsetWidth * 0.05 -
    //     25;
      Howler.volume(per);
    }

    function mute() {
      var audio = audios.value[index.value].howl;

      if (audio) {
        mutePlayer.value = !mutePlayer.value;

        mutePlayer.value ? audio.mute(true) : audio.mute(false);
      }
    }

    function onPlayerTimeUpdated () {
      var sound = audios.value[index.value].howl;
      if (sound) {
        var currentTime = sound.seek()
        if (Number.parseInt(currentTime) % loggingOffset === 0 && Number.parseInt(currentTime) !== lastLoggedTime.value) {
          prepareLogData(currentTime)
        }
      }
    }

    function prepareLogData(currentTime) {
      var secondsToLog = Number.parseInt(currentTime - lastLoggedTime.value)
      secondsToLog = secondsToLog >= 0 ? secondsToLog : 0
      var progress = Number.parseInt(currentTime * 1000)
      lastLoggedTime.value = Number.parseInt(currentTime)
      if(secondsToLog == 0 && progress > 0) {
        return
      }
      logListeningToServer(secondsToLog, progress)
    }

    let secondsToLogToServer = ref(0)
    let progressToLogToServer = ref(0)
    function logListeningToServer(secondsToLog, progressMilliseconds) {
      secondsToLogToServer.value = secondsToLog
      progressToLogToServer.value = progressMilliseconds
      // logListeningToServerFlag.value += 1
    }

    function closePlayer() {
      stop();
      usePlayer().isOpen.value = false;
      usePlayer().podcast.value = {};
    }

    watch(() => props.podcast, (newCount, oldCount) => {
      stop();

      audios.value = [{
        url: config.public.BASE_URL + props.podcast.url,
      }];

      play();
    });

    onMounted(() => {
      play();
    });

    onUnmounted(() => {
      stop();
    });
</script>

<template>
  <div dir="ltr" class="fixed inset-x-0 bottom-0 z-40 p-6 bg-gray-200 dark:bg-gray-800 md:inset-x-auto md:w-full md:max-w-sm md:right-16 md:bottom-20 md:rounded-xl">
    <div class="relative hidden w-full transition-all duration-200 ease-in rounded-lg md:block h-44 hover:h-96 shrink-0">
        <img class="object-cover object-top w-full h-full rounded-lg" :src="config.public.BASE_URL + podcast.poet.thumbnail" alt="">
    </div>

    <div class="flex flex-wrap items-center justify-end flex-1 md:mt-4 gap-x-4">
      
      <div>
        <button @click="closePlayer()" class="p-3 text-white transition-colors duration-200 bg-gray-300 rounded-full dark:bg-slate-600 hover:bg-slate-500 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3" stroke="currentColor" class="w-4 h-4 sm:h-5 sm:w-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="flex flex-1 justify-end">
        <div class="text-right">
          <h1 v-text="podcast.name_ar" class="w-48 xs:w-64 sm:w-full md:w-48 font-semibold text-gray-700 truncate dark:text-white dakr:text-white sm:text-lg"></h1>
          <p v-text="podcast.poet.name_ar" class="w-48 xs:w-64 sm:w-full md:w-48 text-sm text-gray-500 truncate w-full sm:text-base dark:text-gray-400"></p>
        </div>
      </div>

      <div class="flex items-center justify-end gap-2">
        <button v-if="!pauseTrack" @click="play()" class="p-3 text-white transition-colors duration-200 bg-green-400 rounded-full dark:bg-green-600 hover:bg-green-500 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 sm:h-5 sm:w-5">
            <path fill-rule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clip-rule="evenodd" />
          </svg>
        </button>
  
        <button v-else @click="pause()" class="p-3 text-white transition-colors duration-200 bg-gray-400 rounded-full dark:bg-slate-600 hover:bg-slate-500 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 sm:h-5 sm:w-5">
            <path fill-rule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <div class="flex items-center mt-4 gap-x-3">
      <p class="hidden text-sm text-gray-700 dark:text-white sm:block " v-text="timer"></p>
      <div @click="seek($event)" ref="progress" class="w-full overflow-hidden bg-white rounded-lg cursor-pointer dark:bg-gray-700">
        <div class="h-1.5 bg-sky-500 rounded-full" :style="{'width' : step + '%'}"></div>
      </div>
      <p class="hidden text-sm text-gray-700 dark:text-white sm:block " v-text="duration"></p>
    </div>
  </div>
</template>
