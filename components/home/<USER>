<script setup>
    const props = defineProps({
        title:  String,
        api:  String,
        href:  String,
    });

    const config = useRuntimeConfig();

    const poetries = ref([]);

    const pending = ref(true);

    const fetch = async () => {
        const { data: response } = await $fetch(config.public.BASE_URL_API + props.api);

        poetries.value = response;
        pending.value = false;
    }

    onBeforeMount(() => {
        fetch();
    });
</script>

<template>
    <section>
        <div class="relative z-10 pb-16 xl:mx-auto xl:max-w-7xl xl:px-8">
            <div class="px-4 flex items-center justify-between sm:px-6 lg:px-8 xl:px-0">
                <h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white" v-text="title"></h2>
                <NuxtLink :to="href" class="text-sm font-semibold text-sky-600 hover:text-sky-500 dark:text-sky-300 dark:hover:text-sky-400 sm:block">
                    تصفح المزيد
                </NuxtLink>
            </div>

            <div  class="flow-root mt-6">
                <div class="-my-2">
                    <div class="box-content relative py-2 overflow-x-auto h-80 xl:overflow-visible">
                        <card-skeleton v-if="pending" :num="5" />
                        
                        <div v-else class="absolute flex px-4 gap-x-8 min-w-screen-xl sm:px-6 lg:px-8 xl:relative xl:grid xl:grid-cols-5 xl:gap-x-8 xl:px-0">
                            <poetry-card  v-for="poetry in poetries" :key="poetry.id" :data="poetry" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>
