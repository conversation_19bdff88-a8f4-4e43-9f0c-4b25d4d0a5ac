<script setup>

const {
        middlePages = [],
        currentPage
    } = defineProps({
        middlePages: Array,
        currentPage: String,
    });

</script>

<template>
    <div class="flex items-center flex-wrap py-4 overflow-x-auto whitespace-nowrap mb-6">
        <NuxtLink to="/" class="flex gap-x-4 text-gray-600 dark:text-gray-200 hover:text-blue-500 dark:hover:text-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
            </svg>


            <span>الرئيسية</span>
        </NuxtLink>


        <div v-for="page in middlePages" :key="page.title" class="flex items-center overflow-x-auto whitespace-nowrap">
            <span class="mx-2 text-gray-500 dark:text-gray-300 rtl:-scale-x-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
            </span>
    
            <NuxtLink :to="page.link" class="text-gray-600 dark:text-gray-200 hover:underline">
                {{ page.title }}
            </NuxtLink>
        </div>
        

        <span class="mx-2 text-gray-500 dark:text-gray-300 rtl:-scale-x-100">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
        </span>

        <a class="text-gray-400 dark:text-gray-300">
            {{ currentPage }}
        </a>
    </div>
</template>