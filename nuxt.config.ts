// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    target: 'static',
    ssr: false,
    runtimeConfig: {
        public: {
            BASE_URL: process.env.BASE_URL,
            BASE_URL_API: process.env.BASE_URL_API
        }
    },
    app: {
        head: {
            meta: [
                { charset: "utf-8" },
                {
                    name: "viewport",
                    content: "width=device-width, initial-scale=1",
                },
                {
                    hid: "description",
                    name: "description",
                    content:
                        "قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.",
                },
                { name: "twitter:card", content: "summary_large_image" },
                {
                    hid: "twitter:title",
                    name: "twitter:title",
                    content: "قوافي، للشِعر منبر",
                },
                {
                    hid: "twitter:description",
                    name: "twitter:description",
                    content:
                        "قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.",
                },
                {
                    hid: "twitter:image",
                    name: "twitter:image",
                    content:
                        "./thumbnail.webp",
                },
                {
                    hid: "og:title",
                    property: "og:title",
                    content: "قوافي، للشِعر منبر",
                },
                { property: "og:site_name", content: "Qawafi" },
                { hid: "og:type", property: "og:type", content: "website" },
                {
                    hid: "og:image",
                    property: "og:image",
                    content:
                        "./thumbnail.webp",
                },
                {
                    hid: "og:description",
                    property: "og:description",
                    content:
                        "قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.",
                },
                {
                    hid: "Content-Security-Policy",
                    name: "Content-Security-Policy",
                    content: "connect-src qawafi.mvas.digital; style-src 'slef' qawafi.sd.zain.com qawafi.mvas.digital fonts.googleapis.com 'unsafe-inline'; script-src 'self' qawafi.sd.zain.com qawafi.mvas.digital 'unsafe-inline' 'unsafe-eval';",
                }
            ],
            link: [
                { rel: 'icon', type: 'image/x-icon', href: '/favicon.svg' }
            ]
        },
    },
    css: ['~/assets/css/app.css'],
    postcss: {
        plugins: {
            tailwindcss: {},
            autoprefixer: {},
        },
    },
    components: [
        { path: '~/components/home', prefix: 'home' },
        { path: '~/components/profile', prefix: 'profile' },
        '~/components'
    ]
})
