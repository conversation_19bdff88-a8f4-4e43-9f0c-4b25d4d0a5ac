<script setup>
    useHead({
        title: 'تسجيل الدخول - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const phone = ref('');
    const error_message = ref(null);
    const success_message = ref(null);
    const progress = ref(false);

    const validateInput = () => {
        phone.value = phone.value.replace(/\D/g, '');

      if (phone.value.length > 9) {
        phone.value = phone.value.slice(0, 9);
      }
    }

    const login = async () => {
        if (progress.value) return;

        error_message.value = null;
        progress.value = true;

        const res = await $fetch(config.public.BASE_URL_API + '/auth/login?msisdn=249' + phone.value , {
            method: 'POST'
        });

        if (res.status == 200) {
            success_message.value = res.message;

            setTimeout(() => {
                navigateTo("/?ref=DSP&" + "p=" + phone.value + "&t=" + res.token);
            }, 1000);
        } else {
            error_message.value = res.message;
            progress.value = false;
            return;
        }
    }
</script>

<template>
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="flex items-center justify-center">
            <form class="w-full max-w-md pt-10" @submit.prevent="login">
                <h1 class="text-2xl font-semibold text-gray-800 capitalize sm:text-3xl dark:text-white">تسجيل الدخول</h1>
    
                <div class="mt-8 mb-4">أدخل رقم هاتفك</div>
                <div dir="ltr" class="relative flex items-center">
                    <span class="absolute font-semibold text-gray-800 dark:text-white left-4">
                        249
                    </span>
    
                    <input v-model="phone" type="tel" @input="validateInput" class="block w-full py-3 text-gray-700 bg-white border rounded-lg px-14 dark:bg-gray-900 dark:text-gray-300 dark:border-gray-600 focus:border-sky-400 dark:focus:border-sky-300 focus:ring-sky-300 focus:outline-none focus:ring focus:ring-opacity-40" placeholder="912345678">
                </div>

                <p class="mt-2 text-sm text-center text-green-600" v-if="success_message" v-text="error_message"></p>
                <p class="mt-2 text-sm text-center text-red-500" v-if="error_message" v-text="error_message"></p>

                <div class="mt-4">
                    <button :disabled="phone.length != 9 || progress" type="submit" class="w-full px-6 py-3 text-sm font-medium tracking-wide text-white capitalize transition-colors duration-300 transform rounded-lg bg-sky-500 hover:bg-sky-400 focus:outline-none focus:ring focus:ring-sky-300 focus:ring-opacity-50 disabled:cursor-not-allowed">
                        <span v-if="!progress">تسجيل الدخول</span>
                        <span v-else>جارِ تسجيل الدخول..</span>
                    </button>

                    <div class="mt-6 text-center ">
                        <a href="https://dsplp.sd.zain.com/?p=7596994161" class="text-sm text-sky-500 hover:underline dark:text-sky-400">
                            أو اضعط هنا للإشتراك
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>
