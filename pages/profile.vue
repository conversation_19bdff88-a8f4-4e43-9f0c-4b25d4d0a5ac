<script setup>
    useHead({
        title: 'الشعراء - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const pending = ref(true);

    const processing = ref(false);

    const preferences = ref([]);
    const phone = ref('N/A');

    const fetchPreferences = async () => {
        const response = await $fetch(config.public.BASE_URL_API + '/settings/my-preferences', {
            headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
        });

        preferences.value = response;
        pending.value = false;
    }

    const deletePreference = async (id) => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const response = await $fetch(config.public.BASE_URL_API + '/settings/delete-preference/' + id, {
            method: 'POST',
            headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') },
        });

        fetchPreferences();

        processing.value = false;
    }

    const getUserData = async () => {
        if (localStorage.getItem('user_phone')) {
            phone.value = localStorage.getItem('user_phone');
        }
    }

    onMounted(() => {
        getUserData();
        fetchPreferences();
    });
</script>

<template>
    <main class="max-w-5xl mx-auto px-4 max-w-7xl sm:px-6 lg:px-8">
        <div class="pb-6 border-b border-gray-200 dark:border-gray-700 md:flex md:items-baseline md:justify-between">
            <breadcrumbs current-page="الملف الشخصي و اللإعدادات" />

            <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">الملف الشخصي</h1>
        </div>
        <main class="pt-6">
            <div class="mb-12">
                <div>
                    <img class="object-cover w-16 h-16 rounded-full ring ring-gray-300 dark:ring-gray-600" 
                        src="https://img.freepik.com/free-vector/young-man-with-glasses-illustration_1308-174706.jpg?t=st=1735414595~exp=1735418195~hmac=828ae390645683e5f268ed5470ce6a55e9bf90d39282186aed5b432c677d31c2&w=740" 
                        alt="">
                </div>
                <div class="mt-4 text-">
                    <div class="text-gray-600 dark:text-white/50">رقم الهاتف</div>
                    <h2 class="text-lg font-bold text-sky-600 dark:text-sky-400" v-text="phone"></h2>
                </div>
            </div>
        </main>

        <div class="pb-6 border-b border-gray-200 dark:border-gray-700 md:flex md:items-baseline md:justify-between">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">إعدادات التفضيلات</h1>
        </div>
        <main class="pt-6">
            <div class="mb-8">
                <div class="text-gray-700 dark:text-white/60 text-sm mb-4">
                    هذه قائمة المحتوى الذي نعتقدأنه مفضل لديك، ويتم إقتراح المحتوى في الصفحة الرئيسية بناءً على هذه القائمة. يكنك دائماً تخصيصها كما تريد.
                </div>
                <div class="flex flex-wrap gap-x-4 gap-y-2">
                    <span v-for="preference in preferences" :key="preference.id" class="font-normal text-sky-500 dark:text-sky-400 text-lg bg-sky-50 dark:bg-sky-900 rounded-lg px-4 py-0">
                        {{ preference.name_ar }}
                        <button @click="deletePreference(preference.id)" class="mr-2 px-1 text-red-400">x</button>
                    </span>
                </div>

                <div v-if="pending" class="max-w-5xl mx-auto animate-pulse">
                    
                    <div class="flex flex-wrap gap-x-4 gap-y-2 mt2">
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                        <div class="px-10 py-3 bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                    </div>
                </div>

                <div v-if="!pending && preferences.length == 0" class="text-center mt-8 bg-gray-50 py-6">
                    <p class="text-gray-700 dark:text-white mb-1">لم يتم العثور على أي تفضيلات مطابقة لك</p>
                    <p class="text-gray-500 dark:text-white text-sm">يرجى التفاعل مع المحتوى حتى يتم إضافة تفضيلاتك للمحتوى</p>
                </div>
            </div>
        </main>
    </main>
</template>
