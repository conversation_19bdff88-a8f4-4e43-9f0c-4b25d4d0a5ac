<script setup>
    useHead({
        title: 'الشعراء - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const { id } = useRoute().params;
    
    const poet = ref([]);

    const podcasts = ref([]);

    const pending = ref(true);

    let url;

    const fetch = async () => {
        const poetData = await $fetch(
            config.public.BASE_URL_API + '/poets/' + id + '/show'
        ).catch(() => { navigateTo('/404') });

        const { data: podcastsData }  = await $fetch(config.public.BASE_URL_API + '/poets/' + id + '/podcasts');

        poet.value = poetData;

        podcasts.value = podcastsData;

        pending.value = false;

        url = window.location;
    }

    onBeforeMount(() => {
        fetch();
    });
</script>

<template>
    <div class="relative px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div v-if="pending">
            <div class="relative z-10 flex flex-col items-center justify-between max-w-5xl mx-auto animate-pulse sm:flex-row sm:items-center">
                <div class="flex flex-col w-full sm:items-center sm:flex-row gap-x-5">
                    <div class="relative bg-gray-300 rounded-lg h-80 sm:w-40 sm:h-40 dark:bg-gray-800"></div>
                    
                    <div class="mt-4 text-center sm:text-right sm:mt-0">
                        <h1 class="py-1.5 text-2xl font-bold tracking-tight bg-gray-400 rounded-full w-36 dark:bg-gray-800 md:text-3xl"></h1>
                        <div class=" w-48 py-1.5 rounded-full mt-3 bg-gray-300 dark:bg-gray-800"></div>
                    </div>
                </div>
    
                <div class="mt-3 sm:mt-0  w-48 py-1.5 rounded-full bg-gray-300 dark:bg-gray-800"></div>
            </div>
            
            <div class="max-w-5xl mx-auto mt-16 animate-pulse">
                <h2 class="text-xl  w-48 py-1.5 rounded-full bg-gray-300 dark:bg-gray-800"></h2>
                
                <div class="mt-4 space-y-6 ">
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                </div>
            </div>
        </div>

        <div v-else>
            <breadcrumbs :current-page="poet.name_ar" :middle-pages="[{title: 'الشعراء', link: `/poetries` }]" />

            <div class="relative z-10 flex flex-col items-center justify-between max-w-7xl mx-auto sm:flex-row sm:items-center">
                <div class="flex flex-col w-full sm:items-center sm:flex-row gap-x-5">
                    <div class="relative rounded-lg h-80 sm:w-40 sm:h-40">
                        <img class="object-cover w-full h-full rounded-lg shadow-lg shadow-gray-200 dark:shadow-gray-800" :src="config.public.BASE_URL + poet.thumbnail" alt="">
                    </div>
                    
                    <div class="mt-4 text-center sm:text-right sm:mt-0">
                        <h2 class="font-bold text-gray-800 dark:text-white" v-text="poet.name_ar"></h2>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 " v-text="poet.description_ar"></p>
                    </div>
                </div>
    
                <div class="mt-2 sm:mt-0">
                    <div class="flex items-center justify-center -mx-2">
                        <a target="_blank" :href="'https://web.facebook.com/sharer.php?u=' + url" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.00195 12.002C2.00312 16.9214 5.58036 21.1101 10.439 21.881V14.892H7.90195V12.002H10.442V9.80204C10.3284 8.75958 10.6845 7.72064 11.4136 6.96698C12.1427 6.21332 13.1693 5.82306 14.215 5.90204C14.9655 5.91417 15.7141 5.98101 16.455 6.10205V8.56104H15.191C14.7558 8.50405 14.3183 8.64777 14.0017 8.95171C13.6851 9.25566 13.5237 9.68693 13.563 10.124V12.002H16.334L15.891 14.893H13.563V21.881C18.8174 21.0506 22.502 16.2518 21.9475 10.9611C21.3929 5.67041 16.7932 1.73997 11.4808 2.01722C6.16831 2.29447 2.0028 6.68235 2.00195 12.002Z" fill="currentColor"></path>
                            </svg>
                        </a>

                        <a target="_blank" :href="'https://twitter.com/intent/tweet?text=' + poet.name_ar + '&url=' + url" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19.995 6.68799C20.8914 6.15208 21.5622 5.30823 21.882 4.31399C21.0397 4.81379 20.118 5.16587 19.157 5.35499C17.8246 3.94552 15.7135 3.60251 14.0034 4.51764C12.2933 5.43277 11.4075 7.37948 11.841 9.26999C8.39062 9.09676 5.17598 7.4669 2.99702 4.78599C1.85986 6.74741 2.44097 9.25477 4.32502 10.516C3.64373 10.4941 2.97754 10.3096 2.38202 9.97799C2.38202 9.99599 2.38202 10.014 2.38202 10.032C2.38241 12.0751 3.82239 13.8351 5.82502 14.24C5.19308 14.4119 4.53022 14.4372 3.88702 14.314C4.45022 16.0613 6.06057 17.2583 7.89602 17.294C6.37585 18.4871 4.49849 19.1342 2.56602 19.131C2.22349 19.1315 1.88123 19.1118 1.54102 19.072C3.50341 20.333 5.78739 21.0023 8.12002 21C11.3653 21.0223 14.484 19.7429 16.7787 17.448C19.0734 15.1531 20.3526 12.0342 20.33 8.78899C20.33 8.60299 20.3257 8.41799 20.317 8.23399C21.1575 7.62659 21.8828 6.87414 22.459 6.01199C21.676 6.35905 20.8455 6.58691 19.995 6.68799Z" fill="currentColor"></path>
                            </svg>
                        </a>
        
                        <a target="_blank" :href="'https://wa.me/?text=' + url" data-action="share/whatsapp/share" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                            <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.3945 2.66016C14.1328 3.43359 14.6953 4.27734 15.1172 5.22656C15.5391 6.17578 15.75 7.16016 15.75 8.17969C15.75 9.58594 15.3633 10.8867 14.6602 12.082C13.957 13.2773 12.9727 14.2266 11.7773 14.9297C10.582 15.6328 9.28125 15.9844 7.875 15.9844C6.53906 15.9844 5.27344 15.668 4.14844 15.0352L0 16.125L1.125 12.082C0.421875 10.8867 0.0703125 9.58594 0.0703125 8.17969C0.0703125 6.77344 0.421875 5.47266 1.125 4.27734C1.82812 3.08203 2.77734 2.13281 3.97266 1.42969C5.16797 0.726562 6.46875 0.375 7.875 0.375C8.89453 0.375 9.87891 0.585938 10.8281 0.972656C11.7773 1.39453 12.6211 1.95703 13.3945 2.66016ZM7.875 14.6836C9.03516 14.6836 10.125 14.4023 11.1445 13.8047C12.1289 13.2422 12.9375 12.4336 13.5352 11.4492C14.1328 10.4648 14.4492 9.375 14.4492 8.17969C14.4492 7.33594 14.2734 6.52734 13.9219 5.71875C13.5703 4.94531 13.0781 4.24219 12.4453 3.60938C11.8125 2.97656 11.1094 2.51953 10.3359 2.16797C9.5625 1.85156 8.71875 1.67578 7.875 1.67578C6.67969 1.67578 5.58984 1.99219 4.60547 2.55469C3.62109 3.15234 2.8125 3.96094 2.25 4.94531C1.65234 5.92969 1.37109 7.01953 1.37109 8.17969C1.37109 9.44531 1.6875 10.5703 2.39062 11.625L2.53125 11.8711L1.86328 14.2617L4.32422 13.6289L4.57031 13.7695C5.55469 14.4023 6.67969 14.6836 7.875 14.6836ZM11.4258 9.79688L11.5312 9.86719C11.6719 9.9375 11.7773 10.0078 11.8125 10.043C11.8125 10.1133 11.8125 10.2188 11.8125 10.3945C11.8125 10.6055 11.7422 10.7812 11.6719 10.9922C11.6016 11.2031 11.3906 11.3789 11.1094 11.5898C10.8281 11.8008 10.582 11.9062 10.3711 11.9062C10.0195 11.9766 9.70312 11.9766 9.42188 11.9062C9.07031 11.8359 8.57812 11.6953 8.01562 11.4141C6.82031 10.9219 5.73047 9.9375 4.74609 8.53125L4.67578 8.46094C4.14844 7.75781 3.90234 7.08984 3.90234 6.45703C3.90234 5.85938 4.11328 5.33203 4.53516 4.875L4.57031 4.83984C4.71094 4.69922 4.88672 4.59375 5.09766 4.59375H5.51953C5.58984 4.59375 5.66016 4.62891 5.73047 4.66406C5.80078 4.69922 5.83594 4.76953 5.90625 4.91016L6.50391 6.35156C6.57422 6.49219 6.57422 6.63281 6.53906 6.70312C6.39844 6.94922 6.25781 7.16016 6.08203 7.33594C5.97656 7.44141 5.90625 7.51172 5.90625 7.58203C5.87109 7.65234 5.90625 7.6875 5.97656 7.75781C6.29297 8.39062 6.67969 8.84766 7.13672 9.19922C7.45312 9.48047 7.94531 9.76172 8.61328 10.0781C8.78906 10.1836 8.92969 10.1836 9.03516 10.043C9.31641 9.69141 9.52734 9.44531 9.66797 9.26953C9.70312 9.19922 9.77344 9.12891 9.84375 9.12891C9.91406 9.12891 9.98438 9.16406 10.0898 9.16406C10.2656 9.23438 10.7227 9.44531 11.4258 9.79688Z" fill="currentColor"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="max-w-7xl mx-auto mt-12">
                <h2 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">قائمة الأشعار</h2>
                
                <div class="mt-4 space-y-6">
                    <NuxtLink :to="'/poetries/' + podcast.id" class="flex items-center justify-between p-4 transition-colors duration-200 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl" v-for="podcast in podcasts" :key="podcast">
                        <h2 class="flex items-center gap-x-4">
                            <div class="p-1 text-white rounded-full bg-sky-500 ">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.651a3.75 3.75 0 010-5.303m5.304 0a3.75 3.75 0 010 5.303m-7.425 2.122a6.75 6.75 0 010-9.546m9.546 0a6.75 6.75 0 010 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                </svg>
                            </div>

                            <p class="font-bold text-gray-800 truncate dark:text-white" v-text="podcast.name_ar"></p>
                        </h2>

                        <div class="flex items-center gap-x-4">
                            <p v-if="!podcast.is_premium" class="px-3 py-1 text-sm font-bold rounded-full dark:bg-gray-900/50 text-emerald-400 bg-emerald-100">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                </svg>
                            </p>
                            <p class="text-gray-600 rotate-180 dark:text-gray-300">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                                </svg>
                            </p>
                        </div>
                    </NuxtLink>
                </div>
            </div>
        </div>
    </div>
</template>
