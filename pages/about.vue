<script setup>
    useHead({
        title: 'عن المنصة - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const data = ref([]);

    const pending = ref(true);

    const fetch = async () => {
        const response = await $fetch(config.public.BASE_URL_API + '/dynamic-pages/1500def5-cb14-44da-88fb-ad8f75cdcaa5/show');

        data.value = response;
        pending.value = false;
    }

    onBeforeMount(() => {
        fetch();
    });
</script>

<template>
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div v-if="pending">
            <h1 class="py-1.5 text-2xl font-bold tracking-tight bg-gray-400 rounded-full w-36 dark:bg-gray-800 md:text-3xl"></h1>
    
            <div class="w-3/4 py-1.5 rounded-full mt-6 bg-gray-300 dark:bg-gray-800"></div>
        </div>

        <div v-else>
            <breadcrumbs current-page="عن المنصة" />

            <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl" v-text="data.title_ar"></h1>
    
            <div class="mt-6 text-gray-500 dark:text-gray-300" v-html="data.content_ar"></div>
        </div>
    </div>
</template>
