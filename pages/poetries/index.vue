<script setup>
    import {
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    } from '@headlessui/vue';

    useHead({
        title: 'الشِعر - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const poetries = ref([]);

    const pending = ref(true);

    const processing = ref(false);

    const categories = ref([])

    const searchQuery = ref('');

    const route = useRoute()
    
    const category_filter = route.query.category ?? ''
    const ordering_filter = route.query.ordering ?? 'newest'

    const sortOptions = computed(() => [
        { name: 'الأحدث', key: 'newest', href: '?ordering=newest' + '&category=' + category_filter + '&q=' + searchQuery.value, current: ordering_filter === 'newest' },
        { name: 'الأكثر إستماعاً', key: 'most_listened', href: '?ordering=most_listened' + '&category=' + category_filter + '&q=' + searchQuery.value, current: ordering_filter === 'most_listened' },
        { name: 'الأكثر إعجاباً', key: 'most_liked', href: '?ordering=most_liked' + '&category=' + category_filter + '&q=' + searchQuery.value, current: ordering_filter === 'most_liked' },
    ]);

    const currentSortOption = sortOptions.value.find((option) => option.key == ordering_filter)

    let page = 1;

    const fetchPoetries = async () => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const response  = await $fetch(config.public.BASE_URL_API + '/podcasts/all?page=' + page + '&per_page=20' + '&ordering=' + ordering_filter + '&category=' + category_filter + '&q=' + searchQuery.value, {
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            });

        poetries.value.push(...response.data);

        pending.value = false;

        if (page >= response.meta.last_page) {
            page = null;
            return;
        }

        page = response.meta.current_page + 1;

        processing.value = false;
    }

    const fetchCategories = async () => {
        const response = await $fetch(config.public.BASE_URL_API + '/categories');

        categories.value = response.map((category) => {
            return { name: category.name_ar , current: category_filter == category.id };
        });
    }

    onBeforeMount(() => {
        if (route.query.q) {
            searchQuery.value = route.query.q;
        }

        fetchPoetries();
        fetchCategories()
    });

</script>

<template>
    <main class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="pb-6 border-b border-gray-200 dark:border-gray-700">
            <breadcrumbs :current-page="'الشِعر ' + currentSortOption?.name" />

            <div class="block md:flex items-center justify-between">
                <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">الشِعر
                    <span>{{ currentSortOption?.name }}</span>
                </h1>

                <div class="block md:flex items-center gap-x-6 pt-6 md:pt-0">
                    <div class="flex items-center justify-end gap-x-6">
                        <Menu as="div" class="relative text-right md:inline-block">
                            <div>
                                <MenuButton class="inline-flex items-center justify-center text-sm font-medium text-gray-700 gap-x-1 group hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100">
                                    الاقسام
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="flex-shrink-0 w-5 h-5 text-gray-400 group-hover:text-gray-500">
                                        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                    </svg>
            
                                </MenuButton>
                            </div>
            
                            <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                                <MenuItems class="absolute left-0 z-10 w-40 mt-2 origin-top-right bg-white rounded-md shadow-2xl dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <div class="py-1">
                                    <MenuItem v-slot="{ active }">
                                        <a :href="'/poetries?category='+ '&q=' + searchQuery + '&ordering=' + ordering_filter" :class="[!category_filter ? 'font-bold text-orange-400 dark:text-white' : 'text-gray-500 dark:text-gray-300', active ? 'bg-gray-100 dark:bg-gray-700' : '', 'block px-1 py-2 text-sm']">الكل</a>
                                    </MenuItem>
                                    <MenuItem v-for="option in categories" :key="option.name" v-slot="{ active }">
                                        <a :href="'/poetries?category=' + option.id + '&q=' + searchQuery  + '&ordering=' + ordering_filter" :class="[option.current ? 'font-bold text-blue-400 dark:text-white' : 'text-gray-500 dark:text-gray-300', active ? 'bg-gray-100 dark:bg-gray-700' : '', 'block px-4 py-2 text-sm']">{{ option.name }}</a>
                                    </MenuItem>
                                </div>
                                </MenuItems>
                            </transition>
                        </Menu>
            
                        <Menu as="div" class="relative text-right md:inline-block">
                            <div>
                                <MenuButton class="inline-flex items-center justify-center text-sm font-medium text-gray-700 gap-x-1 group hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100">
                                    ترتيب
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="flex-shrink-0 w-5 h-5 text-gray-400 group-hover:text-gray-500">
                                        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                    </svg>
            
                                </MenuButton>
                            </div>
            
                            <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                                <MenuItems class="absolute left-0 z-10 w-40 mt-2 origin-top-right bg-white rounded-md shadow-2xl dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <div class="py-1">
                                    <MenuItem v-for="option in sortOptions" :key="option.key" v-slot="{ active }">
                                        <a :href="option.href" :class="[option.current ? 'font-medium text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-300', active ? 'bg-gray-100 dark:bg-gray-700' : '', 'block px-4 py-2 text-sm']">{{ option.name }}</a>
                                    </MenuItem>
                                </div>
                                </MenuItems>
                            </transition>
                        </Menu>
                    </div>
                </div>
            </div>
        </div>

        <section class="pt-6 pb-16">
            <card-skeleton v-if="pending" :num="10" />

            <div v-else>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                    <NuxtLink :to="'/poetries/' + poetry.id" v-for="poetry in poetries" :key="poetry.id" class="relative flex flex-col p-6 overflow-hidden rounded-lg h-80 md:h-64 hover:opacity-75 xl:w-auto">
                        <span aria-hidden="true" class="absolute inset-0">
                            <img :src="config.public.BASE_URL + poetry.thumbnail" alt="" class="object-cover object-center w-full h-full">
                        </span>
                        
                        <span aria-hidden="true" class="absolute inset-x-0 bottom-0 via-gray-800 opacity-70 h-2/3 bg-gradient-to-t from-gray-800"></span>
                        
                        <div class="relative flex flex-col items-center justify-center mt-auto">
                            <p v-if="!poetry.is_premium" class="px-3 py-2 text-sm font-bold rounded-full bg-gray-900/50 text-sky-400">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                </svg>
                            </p>
                            <p class="text-xl font-semibold text-center text-white truncate" v-text="poetry.name_ar"></p>
                            <p class="text-xl font-semibold text-center text-sky-400" v-text="poetry.poet.name_ar"></p>
                        </div>
                    </NuxtLink>
                </div>

                <div v-if="page" class="flex items-center justify-center mt-8">
                    <button @click.prevent="fetchPoetries" class="px-6 py-2 text-gray-700 transition-colors duration-200 border border-gray-200 rounded-lg dark:text-white dark:border-white hover:bg-gray-100 dark:hover:bg-gray-800 ">
                        تصفح المزيد
                    </button>
                </div>

                <div v-if="!pending && poetries.length == 0" class="text-center mt-8">
                    <p class="text-gray-700 dark:text-white mb-1">لم يتم العثور على أي شعر مطابق لك</p>
                    <p class="text-gray-500 dark:text-white">يرجى التحقق من حقل البحث أو تجربة كلمات مختلفة</p>
                </div>
            </div>
        </section>


        <section v-if="searchQuery.length > 0">
            <div class="pb-6 border-b border-gray-200 dark:border-gray-700 md:flex md:items-baseline md:justify-between">
                <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">الشعراء</h1>
            </div>
    
            <poets-list :search-only="true" />
        </section>
    </main>

</template>
