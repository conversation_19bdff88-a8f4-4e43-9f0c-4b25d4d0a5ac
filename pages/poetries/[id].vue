<script setup>
    useHead({
        title: 'الشِعر - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const isSubscribed = useSubscribed().isSubscribed;
    const hasActiveBilling = useSubscribed().hasActiveBilling;

    const { id } = useRoute().params;

    const podcast = ref({});

    const category = ref({});

    const pending = ref(true);

    const isFavorite = ref(false);

    const isLike = ref(false);

    const likesCount = ref(0);

    const processing = ref(false);

    let url;

    const fetchPoetry = async () => {
        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/show', {
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            }
        ).catch(() => { navigateTo('/404') });

        podcast.value = data;

        pending.value = false;

        url = window.location;
    }

    const fetchCategory = async (podcast_id) => {
        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/category',
        ).catch(() => {});

        category.value = data;
    }

    const fetchLikesCount = async () => {
        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/likes-count',
        ).catch(() => {});

        likesCount.value = data.likes_count;
    }

    const play = () => {
        usePlayer().podcast.value = podcast.value;
        usePlayer().isOpen.value = true;
    }

    const like = async () => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/like', {
                method: 'POST',
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            }
        ).catch(() => { navigateTo('/404') });
        
        isLike.value = !isLike.value;

        processing.value = false;

        fetchLikesCount();
    }

    const favorite = async () => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/favorite', {
                method: 'POST',
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            }
        ).catch(() => { navigateTo('/404') });
        
        isFavorite.value = !isFavorite.value;

        processing.value = false;

        fetchLikesCount();
    }

    const userStats = async () => {
        const data = await $fetch(
            config.public.BASE_URL_API + '/podcasts/' + id + '/user-stats', {
                headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
            }
        ).catch(() => { navigateTo('/404') });

        isFavorite.value = data.is_favorited;
        isLike.value = data.is_liked;
    }

    onBeforeMount(() => {
        fetchPoetry();
        fetchCategory();
        fetchLikesCount();
        userStats();
    });
</script>

<template>
    <div class="relative px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div v-if="pending">
            <div class="relative z-10 flex flex-col items-center max-w-5xl mx-auto">
                <div class="flex flex-col w-full sm:items-center gap-x-5">
                    <div class="relative bg-gray-300 rounded-lg h-80 sm:w-56 sm:h-56 dark:bg-gray-800"></div>
                    
                    <div class="flex flex-col items-center mt-6 text-center">
                         <h1 class="py-1.5 text-2xl font-bold tracking-tight bg-gray-400 rounded-full w-36 dark:bg-gray-800 md:text-3xl"></h1>
                        <div class=" w-48 py-1.5 rounded-full mt-4 bg-gray-300 dark:bg-gray-800"></div>
                    </div>
                </div>
    
                <div class="mt-3 sm:mt-5  w-48 py-1.5 rounded-full bg-gray-300 dark:bg-gray-800"></div>
            </div>

            <div class="w-32 p-4 mx-auto mt-12 bg-gray-300 rounded-full dark:bg-gray-800"></div>
            <div class="w-full max-w-5xl p-2 mx-auto mt-5 bg-gray-300 rounded-full dark:bg-gray-800"></div>
        </div>

        <div v-else>

            <div class="relative z-10 flex flex-col items-center max-w-5xl mx-auto">

                <breadcrumbs :current-page="podcast.name_ar" :middle-pages="[{title: 'الشعراء', link: `/poetries` },{title: podcast.poet.name_ar, link: `/poets/${podcast.poet.id}` }]" margin-top="0" />

                <div class="flex flex-col w-full sm:items-center gap-x-5">
                    <div class="relative rounded-lg h-80 sm:w-56 sm:h-56 ring ring-white dark:ring-gray-900">
                        <img class="object-cover w-full h-full rounded-lg shadow-lg shadow-gray-200 dark:shadow-gray-800" :src="config.public.BASE_URL + podcast.poet.thumbnail" alt="">
                    </div>
                    
                    <div class="mt-4 text-center">
                        <h2 class="text-lg font-bold text-gray-800 dark:text-white" v-text="podcast.name_ar"></h2>
                        <NuxtLink :to="'/poets/' + podcast.poet.id"><p class="mt-1 text-base text-blue-500 dark:text-blue-400 " v-text="podcast.poet.name_ar"></p></NuxtLink>
                        <p class="mt-2 px-2 pb-1 rounded-md text-sm text-base inline-block text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700" v-text="category.name_ar"></p>
                    </div>
                </div>

                <div class="mt-2 sm:mt-4">
                    <div class="text-sm text-gray-500 dark:text-gray-400 ">
                        <div v-if="likesCount">
                            <strong class="mx-1">{{ likesCount }}</strong> {{ likesCount > 1 ? 'اعجبهم هذا' : 'اعجب بهذا' }}
                        </div>
                        <div v-else>
                            كن اول المعجين بهذا
                        </div>
                    </div>
                </div>
    
                <div class="">
                    <button @click="like" v-if="isSubscribed" class="mx-2 focus:outline-none">
                        <p v-if="isLike" class="flex items-center px-4 py-2 mx-auto mt-4 text-gray-600 transition-colors duration-200 border border-gray-200 rounded-lg dark:border-gray-700 dark:text-white gap-x-2 hover:bg-gray-200 dark:hover:bg-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6 w-5 h-5">
                                <path d="M7.493 18.5c-.425 0-.82-.236-.975-.632A7.48 7.48 0 0 1 6 15.125c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 0 1 2.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 0 0 .322-1.672V2.75A.75.75 0 0 1 15 2a2.25 2.25 0 0 1 2.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 0 1-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 0 0-1.423-.23h-.777ZM2.331 10.727a11.969 11.969 0 0 0-.831 4.398 12 12 0 0 0 .52 3.507C2.28 19.482 3.105 20 3.994 20H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 0 1-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227Z" />
                            </svg>
    
                            ازالة الاعجاب
                        </p>

                        <p v-else class="flex items-center px-4 py-2 mx-auto mt-4 text-gray-600 transition-colors duration-200 border border-gray-200 rounded-lg dark:border-gray-700 dark:text-white gap-x-2 hover:bg-gray-200 dark:hover:bg-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.633 10.25c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 0 1 2.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 0 0 .322-1.672V2.75a.75.75 0 0 1 .75-.75 2.25 2.25 0 0 1 2.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282m0 0h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 0 1-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 0 0-1.423-.23H5.904m10.598-9.75H14.25M5.904 18.5c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 0 1-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 9.953 4.167 9.5 5 9.5h1.053c.472 0 .745.556.5.96a8.958 8.958 0 0 0-1.302 4.665c0 1.194.232 2.333.654 3.375Z" />
                            </svg>

    
                            اعجبني
                        </p>
                    </button>

                    <button @click="favorite" v-if="isSubscribed" class=" focus:outline-none">
                        <p v-if="isFavorite" class="flex items-center px-4 py-2 mx-auto mt-4 text-gray-600 transition-colors duration-200 border border-gray-200 rounded-lg dark:border-gray-700 dark:text-white gap-x-2 hover:bg-gray-200 dark:hover:bg-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-red-500 dark:text-red-400">
                                <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z" />
                            </svg>
    
                            ازالة من المفضلة
                        </p>

                        <p v-else class="flex items-center px-4 py-2 mx-auto mt-4 text-gray-600 transition-colors duration-200 border border-gray-200 rounded-lg dark:border-gray-700 dark:text-white gap-x-2 hover:bg-gray-200 dark:hover:bg-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z" />
                            </svg>

    
                            اضافة للمفضلة
                        </p>
                    </button>
                </div>
            </div>

            <div v-if="!podcast.is_premium || (isSubscribed && hasActiveBilling)" class="flex justify-center mx-auto mt-4">
                <button @click="play()" class="flex items-center px-6 py-2.5 hover:bg-emerald-400 duration-300 transition-colors text-white rounded-lg bg-emerald-500 gap-x-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.91 11.672a.375.375 0 010 .656l-5.603 3.113a.375.375 0 01-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112z" />
                    </svg>

                    تشغيل
                </button>
            </div>

            <div v-else-if="isSubscribed && !hasActiveBilling" class="flex justify-center mt-6">
                <p class="relative inline-flex items-center px-6 py-3 text-sm font-semibold text-white bg-yellow-600 rounded-md shadow-sm cursor-not-allowed gap-x-2 hover:bg-yellow-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-yellow-700">
                    ليس لديك رصيدا كافي
                </p>
            </div>

            <div v-else class="flex justify-center mt-6">
                <a href="https://dsplp.sd.zain.com/?p=7596994161" class="relative inline-flex items-center px-6 py-3 text-sm font-semibold text-white rounded-md shadow-sm gap-x-2 bg-emerald-600 hover:bg-emerald-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-700">
                    الرجاء الإشتراك للإستماع!
                    
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                    </svg>
                </a>
            </div>

            <div class="flex items-center justify-center -mx-2 mt-4">
                <a target="_blank" :href="'https://web.facebook.com/sharer.php?u=' + url" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.00195 12.002C2.00312 16.9214 5.58036 21.1101 10.439 21.881V14.892H7.90195V12.002H10.442V9.80204C10.3284 8.75958 10.6845 7.72064 11.4136 6.96698C12.1427 6.21332 13.1693 5.82306 14.215 5.90204C14.9655 5.91417 15.7141 5.98101 16.455 6.10205V8.56104H15.191C14.7558 8.50405 14.3183 8.64777 14.0017 8.95171C13.6851 9.25566 13.5237 9.68693 13.563 10.124V12.002H16.334L15.891 14.893H13.563V21.881C18.8174 21.0506 22.502 16.2518 21.9475 10.9611C21.3929 5.67041 16.7932 1.73997 11.4808 2.01722C6.16831 2.29447 2.0028 6.68235 2.00195 12.002Z" fill="currentColor"></path>
                    </svg>
                </a>

                <a target="_blank" :href="'https://twitter.com/intent/tweet?text=' + podcast.name_ar + '&url=' + url" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.995 6.68799C20.8914 6.15208 21.5622 5.30823 21.882 4.31399C21.0397 4.81379 20.118 5.16587 19.157 5.35499C17.8246 3.94552 15.7135 3.60251 14.0034 4.51764C12.2933 5.43277 11.4075 7.37948 11.841 9.26999C8.39062 9.09676 5.17598 7.4669 2.99702 4.78599C1.85986 6.74741 2.44097 9.25477 4.32502 10.516C3.64373 10.4941 2.97754 10.3096 2.38202 9.97799C2.38202 9.99599 2.38202 10.014 2.38202 10.032C2.38241 12.0751 3.82239 13.8351 5.82502 14.24C5.19308 14.4119 4.53022 14.4372 3.88702 14.314C4.45022 16.0613 6.06057 17.2583 7.89602 17.294C6.37585 18.4871 4.49849 19.1342 2.56602 19.131C2.22349 19.1315 1.88123 19.1118 1.54102 19.072C3.50341 20.333 5.78739 21.0023 8.12002 21C11.3653 21.0223 14.484 19.7429 16.7787 17.448C19.0734 15.1531 20.3526 12.0342 20.33 8.78899C20.33 8.60299 20.3257 8.41799 20.317 8.23399C21.1575 7.62659 21.8828 6.87414 22.459 6.01199C21.676 6.35905 20.8455 6.58691 19.995 6.68799Z" fill="currentColor"></path>
                    </svg>
                </a>

                <a target="_blank" :href="'https://wa.me/?text=' + url" data-action="share/whatsapp/share" class="p-2 text-gray-600 transition-colors duration-200 rounded-lg dark:hover:bg-gray-800 dark:text-gray-300 hover:bg-gray-100">
                    <svg width="21" height="22" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.3945 2.66016C14.1328 3.43359 14.6953 4.27734 15.1172 5.22656C15.5391 6.17578 15.75 7.16016 15.75 8.17969C15.75 9.58594 15.3633 10.8867 14.6602 12.082C13.957 13.2773 12.9727 14.2266 11.7773 14.9297C10.582 15.6328 9.28125 15.9844 7.875 15.9844C6.53906 15.9844 5.27344 15.668 4.14844 15.0352L0 16.125L1.125 12.082C0.421875 10.8867 0.0703125 9.58594 0.0703125 8.17969C0.0703125 6.77344 0.421875 5.47266 1.125 4.27734C1.82812 3.08203 2.77734 2.13281 3.97266 1.42969C5.16797 0.726562 6.46875 0.375 7.875 0.375C8.89453 0.375 9.87891 0.585938 10.8281 0.972656C11.7773 1.39453 12.6211 1.95703 13.3945 2.66016ZM7.875 14.6836C9.03516 14.6836 10.125 14.4023 11.1445 13.8047C12.1289 13.2422 12.9375 12.4336 13.5352 11.4492C14.1328 10.4648 14.4492 9.375 14.4492 8.17969C14.4492 7.33594 14.2734 6.52734 13.9219 5.71875C13.5703 4.94531 13.0781 4.24219 12.4453 3.60938C11.8125 2.97656 11.1094 2.51953 10.3359 2.16797C9.5625 1.85156 8.71875 1.67578 7.875 1.67578C6.67969 1.67578 5.58984 1.99219 4.60547 2.55469C3.62109 3.15234 2.8125 3.96094 2.25 4.94531C1.65234 5.92969 1.37109 7.01953 1.37109 8.17969C1.37109 9.44531 1.6875 10.5703 2.39062 11.625L2.53125 11.8711L1.86328 14.2617L4.32422 13.6289L4.57031 13.7695C5.55469 14.4023 6.67969 14.6836 7.875 14.6836ZM11.4258 9.79688L11.5312 9.86719C11.6719 9.9375 11.7773 10.0078 11.8125 10.043C11.8125 10.1133 11.8125 10.2188 11.8125 10.3945C11.8125 10.6055 11.7422 10.7812 11.6719 10.9922C11.6016 11.2031 11.3906 11.3789 11.1094 11.5898C10.8281 11.8008 10.582 11.9062 10.3711 11.9062C10.0195 11.9766 9.70312 11.9766 9.42188 11.9062C9.07031 11.8359 8.57812 11.6953 8.01562 11.4141C6.82031 10.9219 5.73047 9.9375 4.74609 8.53125L4.67578 8.46094C4.14844 7.75781 3.90234 7.08984 3.90234 6.45703C3.90234 5.85938 4.11328 5.33203 4.53516 4.875L4.57031 4.83984C4.71094 4.69922 4.88672 4.59375 5.09766 4.59375H5.51953C5.58984 4.59375 5.66016 4.62891 5.73047 4.66406C5.80078 4.69922 5.83594 4.76953 5.90625 4.91016L6.50391 6.35156C6.57422 6.49219 6.57422 6.63281 6.53906 6.70312C6.39844 6.94922 6.25781 7.16016 6.08203 7.33594C5.97656 7.44141 5.90625 7.51172 5.90625 7.58203C5.87109 7.65234 5.90625 7.6875 5.97656 7.75781C6.29297 8.39062 6.67969 8.84766 7.13672 9.19922C7.45312 9.48047 7.94531 9.76172 8.61328 10.0781C8.78906 10.1836 8.92969 10.1836 9.03516 10.043C9.31641 9.69141 9.52734 9.44531 9.66797 9.26953C9.70312 9.19922 9.77344 9.12891 9.84375 9.12891C9.91406 9.12891 9.98438 9.16406 10.0898 9.16406C10.2656 9.23438 10.7227 9.44531 11.4258 9.79688Z" fill="currentColor"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</template>
