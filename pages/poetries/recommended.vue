<script setup>
    import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    MenuItems,
    } from '@headlessui/vue';

    useHead({
        title: 'الشِعر - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const poetries = ref([]);

    const pending = ref(true);

    const processing = ref(false);

    let page = 1;

    const fetchPoetries = async () => {
        if (processing.value === true) {
            return;
        }

        processing.value = true;

        const response  = await $fetch(config.public.BASE_URL_API + '/podcasts/recommended?page=' + page + '&per_page=20');

        poetries.value.push(...response.data);

        pending.value = false;

        if (page >= response.meta.last_page) {
            page = null;
            return;
        }

        page = response.meta.current_page + 1;

        processing.value = false;
    }

    onBeforeMount(() => {
        fetchPoetries();
    });
</script>

<template>
    <main class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="pb-6 border-b border-gray-200 dark:border-gray-700">
            <breadcrumbs current-page="مخصص لك" />
            
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">مخصص لك</h1>
            </div>
        </div>

        <section class="pt-6 pb-24">
            <card-skeleton v-if="pending" :num="10" />

            <div v-else>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                    <NuxtLink :to="'/poetries/' + poetry.id" v-for="poetry in poetries" :key="poetry.id" class="relative flex flex-col p-6 overflow-hidden rounded-lg h-80 md:h-64 hover:opacity-75 xl:w-auto">
                        <span aria-hidden="true" class="absolute inset-0">
                            <img :src="config.public.BASE_URL + poetry.thumbnail" alt="" class="object-cover object-center w-full h-full">
                        </span>
                        
                        <span aria-hidden="true" class="absolute inset-x-0 bottom-0 via-gray-800 opacity-70 h-2/3 bg-gradient-to-t from-gray-800"></span>
                        
                        <div class="relative flex flex-col items-center justify-center mt-auto">
                            <p v-if="!poetry.is_premium" class="px-3 py-2 text-sm font-bold rounded-full bg-gray-900/50 text-sky-400">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                </svg>
                            </p>
                            <p class="text-xl font-semibold text-center text-white truncate" v-text="poetry.name_ar"></p>
                            <p class="text-xl font-semibold text-center text-sky-400" v-text="poetry.poet.name_ar"></p>
                        </div>
                    </NuxtLink>
                </div>

                <div v-if="page" class="flex items-center justify-center mt-8">
                    <button @click.prevent="fetchPoetries" class="px-6 py-2 text-gray-700 transition-colors duration-200 border border-gray-200 rounded-lg dark:text-white dark:border-white hover:bg-gray-100 dark:hover:bg-gray-800 ">
                        تصفح المزيد
                    </button>
                </div>
            </div>
        </section>
    </main>
</template>
