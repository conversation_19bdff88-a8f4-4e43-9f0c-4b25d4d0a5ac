<script setup>
    useHead({
        title: 'المفضلة - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const podcasts = ref([]);

    const pending = ref(true);

    const isSubscribed = useSubscribed().isSubscribed;

    const fetch = async () => {
        if (!isSubscribed) {
            navigateTo('/');
        }

        const { data: response } = await $fetch(config.public.BASE_URL_API + '/podcasts/library/favorites', {
            headers: { Authorization: 'Bearer ' + localStorage.getItem('user_access_token') }
        });

        podcasts.value = response;
        pending.value = false;
    }

    onBeforeMount(() => {
        fetch();
    });
</script>

<template>
    <div class="relative px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div v-if="pending">
            <div class="max-w-5xl mx-auto animate-pulse">
                <h2 class="text-xl  w-48 py-1.5 rounded-full bg-gray-300 dark:bg-gray-800"></h2>
                
                <div class="mt-4 space-y-6 ">
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                   <div class="p-8 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl"></div>
                </div>
            </div>
        </div>

        <div v-else>
            <div class="max-w-5xl mx-auto">
                <div class="pb-6 border-b border-gray-200 dark:border-gray-700 md:flex md:items-baseline md:justify-between">
                    <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white md:text-3xl">المفضلة</h1>
                </div>

                <div class="mt-4 space-y-6">
                    <NuxtLink :to="'/poetries/' + podcast.id" class="flex items-center justify-between p-4 transition-colors duration-200 bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-100 rounded-xl" v-for="podcast in podcasts" :key="podcast">
                        <h2 class="flex items-center gap-x-4">
                            <div class="p-1 text-white rounded-full bg-sky-500 ">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.651a3.75 3.75 0 010-5.303m5.304 0a3.75 3.75 0 010 5.303m-7.425 2.122a6.75 6.75 0 010-9.546m9.546 0a6.75 6.75 0 010 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                </svg>
                            </div>

                            <p class="font-bold text-gray-800 truncate dark:text-white" v-text="podcast.name_ar"></p>
                        </h2>

                        <div class="flex items-center gap-x-4">
                            <p v-if="!podcast.is_premium" class="px-3 py-1 text-sm font-bold rounded-full dark:bg-gray-900/50 text-emerald-400 bg-emerald-100">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                </svg>
                            </p>
                            <p class="text-gray-600 rotate-180 dark:text-gray-300">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                                </svg>
                            </p>
                        </div>
                    </NuxtLink>
                </div>
            </div>
        </div>
    </div>
</template>
