<script setup>
    useHead({
        title: 'الرئيسية - قوافي',
        meta: [
            { name: 'description', content: 'قوافي، هي بوابة قصائد بفئات مختلفة وعدد من الشعراء المشهورين والناشئين ، يمكن للمشتركين الاستماع إلى شعرائهم المفضلين في أي وقت وفي أي مكان عبر الإنترنت.' }
        ],
    });

    const config = useRuntimeConfig();

    const route = useRoute();

    const urlHasToken = async () => {
        if (route.query.t) {
            const res = await $fetch(config.public.BASE_URL_API + '/auth/me', {
                headers: { Authorization: 'Bearer ' + route.query.t }
            });

            if (res.is_subscribed) {
                useSubscribed().isSubscribed.value = true;

                if (res.has_active_billing) {
                    useSubscribed().hasActiveBilling.value = true;
                }

                localStorage.setItem('user_access_token', route.query.t);
                localStorage.setItem('user_phone', route.query.p);
            }
        }
    }

    onMounted(() => {
        urlHasToken();
    });
</script>

<template>
    <hero />

    <home-poetry-section 
        title="مخصص لك"
        api="/podcasts/recommended?per_page=5"
        href="/poetries/recommended"
    />

    <home-poetry-section 
        title="الأكثر إستماعاً"
        api="/podcasts/all?ordering=most_listened&per_page=5"
        href="/poetries?ordering=most_listened"
    />

    <home-poetry-section 
        title="جديد"
        api="/podcasts/all?ordering=newest&per_page=5"
        href="/poetries?ordering=newest"
    />

    <home-poetry-section
        title="قصائد مختارة"
        api="/podcasts/chosen"
        href="/poetries/chosen"
    />
</template>
